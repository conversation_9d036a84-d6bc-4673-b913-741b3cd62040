"use client";

import { useState, useCallback } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface ColumnInfo {
  id: string;
  title: string;
  canHide: boolean;
}

interface ColumnBadgeProps {
  column: ColumnInfo;
  isVisible: boolean;
  onVisibilityChange: (checked: boolean) => void;
  canMoveLeft: boolean;
  canMoveRight: boolean;
  onMoveLeft: () => void;
  onMoveRight: () => void;
}

function ColumnBadge({
  column,
  isVisible,
  onVisibilityChange,
  canMoveLeft,
  canMoveRight,
  onMoveLeft,
  onMoveRight,
}: ColumnBadgeProps) {
  return (
    <div
      className={`relative inline-flex items-center gap-2 px-3 py-2 rounded-lg border transition-all touch-manipulation cursor-pointer ${
        isVisible
          ? "bg-primary/10 border-primary/20 text-primary"
          : "bg-muted/50 border-muted text-muted-foreground"
      } hover:shadow-sm hover:scale-[1.02]`}
      onClick={() => onVisibilityChange(!isVisible)}
    >
      {/* Left Arrow */}
      <Button
        variant="ghost"
        size="sm"
        className="h-5 w-5 p-0 hover:bg-background/50 touch-manipulation"
        onClick={(e) => {
          e.stopPropagation();
          onMoveLeft();
        }}
        disabled={!canMoveLeft}
      >
        <ChevronLeft className="h-3 w-3" />
      </Button>

      {/* Column Title */}
      <span className="text-xs font-medium leading-none select-none">
        {column.title}
      </span>

      {/* Right Arrow */}
      <Button
        variant="ghost"
        size="sm"
        className="h-5 w-5 p-0 hover:bg-background/50 touch-manipulation"
        onClick={(e) => {
          e.stopPropagation();
          onMoveRight();
        }}
        disabled={!canMoveRight}
      >
        <ChevronRight className="h-3 w-3" />
      </Button>
    </div>
  );
}

interface CompaniesTableColumnCustomizationProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  columns: ColumnInfo[];
  columnVisibility: Record<string, boolean>;
  columnOrder: string[];
  onSave: (visibility: Record<string, boolean>, order: string[]) => void;
}

export function CompaniesTableColumnCustomization({
  open,
  onOpenChange,
  columns,
  columnVisibility,
  columnOrder,
  onSave,
}: CompaniesTableColumnCustomizationProps) {
  const [localVisibility, setLocalVisibility] = useState(columnVisibility);
  const [localOrder, setLocalOrder] = useState(columnOrder);

  // Reset local state when dialog opens
  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      if (newOpen) {
        setLocalVisibility(columnVisibility);
        setLocalOrder(columnOrder);
      }
      onOpenChange(newOpen);
    },
    [columnVisibility, columnOrder, onOpenChange]
  );

  // Handle visibility toggle
  const handleVisibilityChange = useCallback(
    (columnId: string, checked: boolean) => {
      setLocalVisibility((prev) => ({
        ...prev,
        [columnId]: checked,
      }));
    },
    []
  );

  // Handle moving column left
  const handleMoveLeft = useCallback((columnId: string) => {
    setLocalOrder((prev) => {
      const currentIndex = prev.indexOf(columnId);
      if (currentIndex <= 0) return prev;

      const newOrder = [...prev];
      [newOrder[currentIndex - 1], newOrder[currentIndex]] = [newOrder[currentIndex], newOrder[currentIndex - 1]];
      return newOrder;
    });
  }, []);

  // Handle moving column right
  const handleMoveRight = useCallback((columnId: string) => {
    setLocalOrder((prev) => {
      const currentIndex = prev.indexOf(columnId);
      if (currentIndex >= prev.length - 1) return prev;

      const newOrder = [...prev];
      [newOrder[currentIndex], newOrder[currentIndex + 1]] = [newOrder[currentIndex + 1], newOrder[currentIndex]];
      return newOrder;
    });
  }, []);

    if (active.id !== over?.id) {
      setLocalOrder((items) => {
        const oldIndex = items.indexOf(active.id as string);
        const newIndex = items.indexOf(over?.id as string);

        return arrayMove(items, oldIndex, newIndex);
      });
    }

    setActiveId(null);
  }, []);

  // Handle save
  const handleSave = useCallback(() => {
    onSave(localVisibility, localOrder);
    onOpenChange(false);
  }, [localVisibility, localOrder, onSave, onOpenChange]);

  // Handle cancel
  const handleCancel = useCallback(() => {
    setLocalVisibility(columnVisibility);
    setLocalOrder(columnOrder);
    onOpenChange(false);
  }, [columnVisibility, columnOrder, onOpenChange]);

  // Get ordered columns for display
  const orderedColumns = localOrder
    .map((id) => columns.find((col) => col.id === id))
    .filter(Boolean) as ColumnInfo[];

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-h-[80vh] w-[95vw] max-w-[95vw] sm:w-auto sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Personalizează vizualizarea informațiilor</DialogTitle>
          <DialogDescription>
            Selectează coloanele pe care dorești să le afișezi prin bifarea
            căsuțelor și modifică ordinea prin tragerea elementelor. Coloanele
            active sunt evidențiate în albastru.
          </DialogDescription>
        </DialogHeader>

        <div className="max-h-[400px] overflow-y-auto pr-4">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={localOrder} strategy={rectSortingStrategy}>
              <div className="flex flex-wrap gap-2">
                {orderedColumns.map((column) => {
                  const isVisible = localVisibility[column.id] !== false;

                  return (
                    <SortableColumnBadge
                      key={column.id}
                      column={column}
                      isVisible={isVisible}
                      onVisibilityChange={(checked: boolean) =>
                        handleVisibilityChange(column.id, checked)
                      }
                    />
                  );
                })}
              </div>
            </SortableContext>

            <DragOverlay>
              {activeId ? (
                <div className="relative inline-flex items-center gap-2 px-3 py-2 rounded-lg border bg-primary/10 border-primary/20 text-primary shadow-lg rotate-3 scale-105 transition-transform">
                  <GripVertical className="h-3 w-3" />
                  <div className="h-3 w-3 rounded border" />
                  <span className="text-xs font-medium">
                    {columns.find((col) => col.id === activeId)?.title ||
                      "Column"}
                  </span>
                </div>
              ) : null}
            </DragOverlay>
          </DndContext>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={handleCancel}>
            Renunță
          </Button>
          <Button onClick={handleSave} className="bg-sky-500 hover:bg-sky-600">
            OK
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
