"use client";

import { useState, useCallback } from "react";
import { ChevronUp, ChevronDown, GripVertical } from "lucide-react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface ColumnInfo {
  id: string;
  title: string;
  canHide: boolean;
}

interface SortableColumnBadgeProps {
  column: ColumnInfo;
  isVisible: boolean;
  onVisibilityChange: (checked: boolean) => void;
  canMoveUp: boolean;
  canMoveDown: boolean;
  onMoveUp: () => void;
  onMoveDown: () => void;
}

function SortableColumnBadge({
  column,
  isVisible,
  onVisibilityChange,
  canMoveUp,
  canMoveDown,
  onMoveUp,
  onMoveDown,
}: SortableColumnBadgeProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: column.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`relative inline-flex items-center gap-2 px-3 py-2 rounded-lg border transition-all touch-manipulation ${
        isVisible
          ? "bg-primary/10 border-primary/20 text-primary"
          : "bg-muted/50 border-muted text-muted-foreground"
      } ${isDragging ? "shadow-lg" : "hover:shadow-sm"}`}
    >
      {/* Drag Handle */}
      <div
        {...attributes}
        {...listeners}
        className="cursor-grab active:cursor-grabbing p-0.5 hover:bg-background/50 rounded touch-manipulation"
      >
        <GripVertical className="h-3 w-3" />
      </div>

      {/* Checkbox */}
      <Checkbox
        id={`column-${column.id}`}
        checked={isVisible}
        onCheckedChange={onVisibilityChange}
        disabled={!column.canHide}
        className="h-3 w-3"
      />

      {/* Column Title */}
      <label
        htmlFor={`column-${column.id}`}
        className="text-xs font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer select-none"
      >
        {column.title}
      </label>

      {/* Reorder Controls */}
      <div className="flex items-center gap-0.5">
        <Button
          variant="ghost"
          size="sm"
          className="h-5 w-5 p-0 hover:bg-background/50 touch-manipulation"
          onClick={onMoveUp}
          disabled={!canMoveUp}
        >
          <ChevronUp className="h-3 w-3" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-5 w-5 p-0 hover:bg-background/50 touch-manipulation"
          onClick={onMoveDown}
          disabled={!canMoveDown}
        >
          <ChevronDown className="h-3 w-3" />
        </Button>
      </div>
    </div>
  );
}

interface CompaniesTableColumnCustomizationProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  columns: ColumnInfo[];
  columnVisibility: Record<string, boolean>;
  columnOrder: string[];
  onSave: (visibility: Record<string, boolean>, order: string[]) => void;
}

export function CompaniesTableColumnCustomization({
  open,
  onOpenChange,
  columns,
  columnVisibility,
  columnOrder,
  onSave,
}: CompaniesTableColumnCustomizationProps) {
  const [localVisibility, setLocalVisibility] = useState(columnVisibility);
  const [localOrder, setLocalOrder] = useState(columnOrder);

  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Reset local state when dialog opens
  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      if (newOpen) {
        setLocalVisibility(columnVisibility);
        setLocalOrder(columnOrder);
      }
      onOpenChange(newOpen);
    },
    [columnVisibility, columnOrder, onOpenChange]
  );

  // Handle visibility toggle
  const handleVisibilityChange = useCallback(
    (columnId: string, checked: boolean) => {
      setLocalVisibility((prev) => ({
        ...prev,
        [columnId]: checked,
      }));
    },
    []
  );

  // Handle column reordering
  const moveColumn = useCallback(
    (columnId: string, direction: "up" | "down") => {
      setLocalOrder((prev) => {
        const currentIndex = prev.indexOf(columnId);
        if (currentIndex === -1) return prev;

        const newOrder = [...prev];
        const targetIndex =
          direction === "up" ? currentIndex - 1 : currentIndex + 1;

        if (targetIndex < 0 || targetIndex >= newOrder.length) return prev;

        // Swap elements
        [newOrder[currentIndex], newOrder[targetIndex]] = [
          newOrder[targetIndex],
          newOrder[currentIndex],
        ];

        return newOrder;
      });
    },
    []
  );

  // Handle drag end
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setLocalOrder((items) => {
        const oldIndex = items.indexOf(active.id as string);
        const newIndex = items.indexOf(over?.id as string);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  }, []);

  // Handle save
  const handleSave = useCallback(() => {
    onSave(localVisibility, localOrder);
    onOpenChange(false);
  }, [localVisibility, localOrder, onSave, onOpenChange]);

  // Handle cancel
  const handleCancel = useCallback(() => {
    setLocalVisibility(columnVisibility);
    setLocalOrder(columnOrder);
    onOpenChange(false);
  }, [columnVisibility, columnOrder, onOpenChange]);

  // Get ordered columns for display
  const orderedColumns = localOrder
    .map((id) => columns.find((col) => col.id === id))
    .filter(Boolean) as ColumnInfo[];

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-h-[80vh] w-[95vw] max-w-[95vw] sm:w-auto sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Personalizează vizualizarea informațiilor</DialogTitle>
          <DialogDescription>
            Selectează coloanele pe care dorești să le afișezi prin bifarea
            căsuțelor și modifică ordinea prin tragerea elementelor. Coloanele
            active sunt evidențiate în albastru.
          </DialogDescription>
        </DialogHeader>

        <div className="max-h-[400px] overflow-y-auto pr-4">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={localOrder}
              strategy={verticalListSortingStrategy}
            >
              <div className="flex flex-wrap gap-2">
                {orderedColumns.map((column, index) => {
                  const isVisible = localVisibility[column.id] !== false;
                  const canMoveUp = index > 0;
                  const canMoveDown = index < orderedColumns.length - 1;

                  return (
                    <SortableColumnBadge
                      key={column.id}
                      column={column}
                      isVisible={isVisible}
                      onVisibilityChange={(checked: boolean) =>
                        handleVisibilityChange(column.id, checked)
                      }
                      canMoveUp={canMoveUp}
                      canMoveDown={canMoveDown}
                      onMoveUp={() => moveColumn(column.id, "up")}
                      onMoveDown={() => moveColumn(column.id, "down")}
                    />
                  );
                })}
              </div>
            </SortableContext>
          </DndContext>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={handleCancel}>
            Renunță
          </Button>
          <Button onClick={handleSave} className="bg-sky-500 hover:bg-sky-600">
            OK
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
