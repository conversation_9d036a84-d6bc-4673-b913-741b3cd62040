"use client";

import { useState, useCallback } from "react";
import { ChevronUp, ChevronDown, GripVertical } from "lucide-react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface ColumnInfo {
  id: string;
  title: string;
  canHide: boolean;
}

interface SortableColumnItemProps {
  column: ColumnInfo;
  isVisible: boolean;
  onVisibilityChange: (checked: boolean) => void;
  canMoveUp: boolean;
  canMoveDown: boolean;
  onMoveUp: () => void;
  onMoveDown: () => void;
}

function SortableColumnItem({
  column,
  isVisible,
  onVisibilityChange,
  canMoveUp,
  canMoveDown,
  onMoveUp,
  onMoveDown,
}: SortableColumnItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: column.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="flex items-center justify-between p-3 border rounded-lg bg-background touch-manipulation"
    >
      <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
        <div
          {...attributes}
          {...listeners}
          className="cursor-grab active:cursor-grabbing p-1 hover:bg-muted rounded touch-manipulation"
        >
          <GripVertical className="h-4 w-4 text-muted-foreground" />
        </div>
        <Checkbox
          id={`column-${column.id}`}
          checked={isVisible}
          onCheckedChange={onVisibilityChange}
          disabled={!column.canHide}
        />
        <label
          htmlFor={`column-${column.id}`}
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 truncate"
        >
          {column.title}
        </label>
      </div>

      <div className="flex items-center space-x-1 flex-shrink-0">
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 touch-manipulation"
          onClick={onMoveUp}
          disabled={!canMoveUp}
        >
          <ChevronUp className="h-4 w-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0 touch-manipulation"
          onClick={onMoveDown}
          disabled={!canMoveDown}
        >
          <ChevronDown className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

interface CompaniesTableColumnCustomizationProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  columns: ColumnInfo[];
  columnVisibility: Record<string, boolean>;
  columnOrder: string[];
  onSave: (visibility: Record<string, boolean>, order: string[]) => void;
}

export function CompaniesTableColumnCustomization({
  open,
  onOpenChange,
  columns,
  columnVisibility,
  columnOrder,
  onSave,
}: CompaniesTableColumnCustomizationProps) {
  const [localVisibility, setLocalVisibility] = useState(columnVisibility);
  const [localOrder, setLocalOrder] = useState(columnOrder);

  // Sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Reset local state when dialog opens
  const handleOpenChange = useCallback(
    (newOpen: boolean) => {
      if (newOpen) {
        setLocalVisibility(columnVisibility);
        setLocalOrder(columnOrder);
      }
      onOpenChange(newOpen);
    },
    [columnVisibility, columnOrder, onOpenChange]
  );

  // Handle visibility toggle
  const handleVisibilityChange = useCallback(
    (columnId: string, checked: boolean) => {
      setLocalVisibility((prev) => ({
        ...prev,
        [columnId]: checked,
      }));
    },
    []
  );

  // Handle column reordering
  const moveColumn = useCallback(
    (columnId: string, direction: "up" | "down") => {
      setLocalOrder((prev) => {
        const currentIndex = prev.indexOf(columnId);
        if (currentIndex === -1) return prev;

        const newOrder = [...prev];
        const targetIndex =
          direction === "up" ? currentIndex - 1 : currentIndex + 1;

        if (targetIndex < 0 || targetIndex >= newOrder.length) return prev;

        // Swap elements
        [newOrder[currentIndex], newOrder[targetIndex]] = [
          newOrder[targetIndex],
          newOrder[currentIndex],
        ];

        return newOrder;
      });
    },
    []
  );

  // Handle drag end
  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setLocalOrder((items) => {
        const oldIndex = items.indexOf(active.id as string);
        const newIndex = items.indexOf(over?.id as string);

        return arrayMove(items, oldIndex, newIndex);
      });
    }
  }, []);

  // Handle save
  const handleSave = useCallback(() => {
    onSave(localVisibility, localOrder);
    onOpenChange(false);
  }, [localVisibility, localOrder, onSave, onOpenChange]);

  // Handle cancel
  const handleCancel = useCallback(() => {
    setLocalVisibility(columnVisibility);
    setLocalOrder(columnOrder);
    onOpenChange(false);
  }, [columnVisibility, columnOrder, onOpenChange]);

  // Get ordered columns for display
  const orderedColumns = localOrder
    .map((id) => columns.find((col) => col.id === id))
    .filter(Boolean) as ColumnInfo[];

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="max-h-[80vh] w-[95vw] max-w-[95vw] sm:w-auto sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Personalizează vizualizarea informațiilor</DialogTitle>
          <DialogDescription>
            Alege ce informații să apară în tabel. Drag & drop pentru a schimba
            ordinea elementelor.
          </DialogDescription>
        </DialogHeader>

        <div className="max-h-[400px] overflow-y-auto pr-4">
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={localOrder}
              strategy={verticalListSortingStrategy}
            >
              <div className="space-y-2">
                {orderedColumns.map((column, index) => {
                  const isVisible = localVisibility[column.id] !== false;
                  const canMoveUp = index > 0;
                  const canMoveDown = index < orderedColumns.length - 1;

                  return (
                    <SortableColumnItem
                      key={column.id}
                      column={column}
                      isVisible={isVisible}
                      onVisibilityChange={(checked) =>
                        handleVisibilityChange(column.id, checked)
                      }
                      canMoveUp={canMoveUp}
                      canMoveDown={canMoveDown}
                      onMoveUp={() => moveColumn(column.id, "up")}
                      onMoveDown={() => moveColumn(column.id, "down")}
                    />
                  );
                })}
              </div>
            </SortableContext>
          </DndContext>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={handleCancel}>
            Renunță
          </Button>
          <Button onClick={handleSave} className="bg-sky-500 hover:bg-sky-600">
            OK
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
